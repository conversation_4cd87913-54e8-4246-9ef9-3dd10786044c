-- backend_schema.sql
-- Unified backend schema for Wizlop

-- Users/auth tables (NextAuth)
CREATE TABLE IF NOT EXISTS backend_schema.nextauth_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  username VARCHAR(100) UNIQUE,
  name TEXT,
  email TEXT UNIQUE,
  email_verified TIMESTAMP(3),
  image TEXT,
  profile_picture_url TEXT,
  role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'agent', 'superuser')),
  permissions JSONB DEFAULT '[]',
  age INTEGER,
  avatar_url TEXT,
  profile_completed BOOLEAN DEFAULT TRUE,
  password_hash VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS backend_schema.nextauth_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  provider TEXT NOT NULL,
  provider_account_id TEXT NOT NULL,
  refresh_token TEXT,
  access_token TEXT,
  expires_at BIGINT,
  token_type TEXT,
  scope TEXT,
  id_token TEXT,
  session_state TEXT
);

CREATE TABLE IF NOT EXISTS backend_schema.nextauth_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_token TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  expires TIMESTAMP(3) NOT NULL
);

CREATE TABLE IF NOT EXISTS backend_schema.nextauth_verification_tokens (
  identifier TEXT NOT NULL,
  token TEXT NOT NULL,
  expires TIMESTAMP(3) NOT NULL,
  PRIMARY KEY (identifier, token)
);

-- User credits
CREATE TABLE IF NOT EXISTS backend_schema.user_credits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  credits_earned INTEGER DEFAULT 0,
  credits_purchased INTEGER DEFAULT 0,
  credits_used INTEGER DEFAULT 0,
  subscription_type VARCHAR(20) DEFAULT 'none',
  subscription_expires_at TIMESTAMP WITH TIME ZONE,
  gamification_data JSONB DEFAULT '{}',
  verification_tasks_completed INTEGER DEFAULT 0,
  accuracy_rating FLOAT DEFAULT 0.0,
  user_level INTEGER DEFAULT 1,
  total_points_earned INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Credit transactions table for detailed tracking
CREATE TABLE IF NOT EXISTS backend_schema.credit_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('earn', 'purchase', 'use', 'refund')),
  amount INTEGER NOT NULL,
  reason VARCHAR(100) NOT NULL,
  description TEXT,
  metadata JSONB DEFAULT '{}',
  related_entity_type VARCHAR(50), -- 'poi_submission', 'like', 'comment', 'chat_request', etc.
  related_entity_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Daily user limits tracking
CREATE TABLE IF NOT EXISTS backend_schema.user_daily_limits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  limit_date DATE NOT NULL DEFAULT CURRENT_DATE,
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  poi_submissions_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, limit_date)
);

-- User location settings
CREATE TABLE IF NOT EXISTS backend_schema.user_location_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  search_radius INTEGER DEFAULT 5000 CHECK (search_radius >= 100 AND search_radius <= 10000),
  num_candidates INTEGER DEFAULT 5 CHECK (num_candidates >= 1 AND num_candidates <= 20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User location reviews
CREATE TABLE IF NOT EXISTS backend_schema.user_location_reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  poi_id INTEGER,
  user_poi_temp_id INTEGER,
  user_poi_approved_id INTEGER,
  poi_type VARCHAR(20) NOT NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  review_title VARCHAR(200),
  tags JSONB DEFAULT '[]',
  photos JSONB DEFAULT '[]',
  visit_date DATE,
  is_verified BOOLEAN DEFAULT FALSE,
  helpful_votes INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User location visits
CREATE TABLE IF NOT EXISTS backend_schema.user_location_visits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  poi_id INTEGER,
  user_poi_temp_id INTEGER,
  user_poi_approved_id INTEGER,
  poi_type VARCHAR(20) NOT NULL,
  visit_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  visit_duration_minutes INTEGER,
  visit_type VARCHAR(20) CHECK (visit_type IN ('planned', 'spontaneous', 'recommended')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User favorites
CREATE TABLE IF NOT EXISTS backend_schema.user_favorites (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  poi_id INTEGER,
  user_poi_temp_id INTEGER,
  user_poi_approved_id INTEGER,
  poi_type VARCHAR(20) NOT NULL CHECK (poi_type IN ('official', 'user_temp', 'user_approved')),
  favorite_type VARCHAR(20) DEFAULT 'location',
  notes TEXT,
  interaction_metadata JSONB DEFAULT '{}',
  last_interaction_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User added locations
CREATE TABLE IF NOT EXISTS backend_schema.user_added_locations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  latitude DOUBLE PRECISION NOT NULL,
  longitude DOUBLE PRECISION NOT NULL,
  phone_number VARCHAR(20),
  website_url TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  verification_status VARCHAR(20) DEFAULT 'pending',
  geom GEOMETRY(POINT, 4326),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User lists
CREATE TABLE IF NOT EXISTS backend_schema.user_lists (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  list_name VARCHAR(100) NOT NULL,
  description TEXT,
  list_type VARCHAR(20) DEFAULT 'personal',
  poi_ids JSONB DEFAULT '[]',
  tags JSONB DEFAULT '[]',
  is_archived BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Note: user_poi_submissions table removed - functionality moved to spatial_schema.user_pois_temp
-- This eliminates redundancy and simplifies the POI submission workflow

-- User interactions
CREATE TABLE IF NOT EXISTS backend_schema.user_interactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  poi_id INTEGER,
  user_poi_temp_id INTEGER,
  user_poi_approved_id INTEGER,
  poi_type VARCHAR(20) NOT NULL CHECK (poi_type IN ('official', 'user_temp', 'user_approved')),
  interaction_type VARCHAR(20) NOT NULL CHECK (interaction_type IN ('like', 'visit', 'share', 'favorite', 'review')),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Constraints to ensure only one POI reference is set
  CONSTRAINT check_one_poi_type CHECK (
    (poi_type = 'official' AND poi_id IS NOT NULL AND user_poi_temp_id IS NULL AND user_poi_approved_id IS NULL) OR
    (poi_type = 'user_temp' AND poi_id IS NULL AND user_poi_temp_id IS NOT NULL AND user_poi_approved_id IS NULL) OR
    (poi_type = 'user_approved' AND poi_id IS NULL AND user_poi_temp_id IS NULL AND user_poi_approved_id IS NOT NULL)
  )
);

-- Agent/role/permission tables
CREATE TABLE IF NOT EXISTS backend_schema.agent_permissions (
  id SERIAL PRIMARY KEY,
  permission_name VARCHAR(50) UNIQUE NOT NULL,
  permission_description TEXT,
  permission_category VARCHAR(30) DEFAULT 'general',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS backend_schema.user_role_assignments (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'agent', 'superuser')),
  assigned_by UUID REFERENCES backend_schema.nextauth_users(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  notes TEXT,
  UNIQUE(user_id, role)
);

CREATE TABLE IF NOT EXISTS backend_schema.agent_activity_log (
  id SERIAL PRIMARY KEY,
  agent_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  action VARCHAR(50) NOT NULL,
  target_type VARCHAR(30),
  target_id TEXT,
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User location photos
CREATE TABLE IF NOT EXISTS backend_schema.user_location_photos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES backend_schema.nextauth_users(id) ON DELETE CASCADE,
  location_id UUID,
  poi_id INTEGER,
  photo_url TEXT NOT NULL,
  caption TEXT,
  is_public BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Backend triggers
CREATE OR REPLACE FUNCTION backend_schema.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_user_credits_updated_at ON backend_schema.user_credits;
CREATE TRIGGER update_user_credits_updated_at
  BEFORE UPDATE ON backend_schema.user_credits
  FOR EACH ROW EXECUTE FUNCTION backend_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_daily_limits_updated_at ON backend_schema.user_daily_limits;
CREATE TRIGGER update_user_daily_limits_updated_at
  BEFORE UPDATE ON backend_schema.user_daily_limits
  FOR EACH ROW EXECUTE FUNCTION backend_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_location_settings_updated_at ON backend_schema.user_location_settings;
CREATE TRIGGER update_user_location_settings_updated_at
  BEFORE UPDATE ON backend_schema.user_location_settings
  FOR EACH ROW EXECUTE FUNCTION backend_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_favorites_updated_at ON backend_schema.user_favorites;
CREATE TRIGGER update_user_favorites_updated_at
  BEFORE UPDATE ON backend_schema.user_favorites
  FOR EACH ROW EXECUTE FUNCTION backend_schema.update_updated_at_column();

-- Note: user_poi_submissions table removed - functionality moved to spatial_schema.user_pois_temp
-- This eliminates redundancy and simplifies the POI submission workflow

DROP TRIGGER IF EXISTS update_user_location_visits_updated_at ON backend_schema.user_location_visits;
CREATE TRIGGER update_user_location_visits_updated_at
  BEFORE UPDATE ON backend_schema.user_location_visits
  FOR EACH ROW EXECUTE FUNCTION backend_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_location_reviews_updated_at ON backend_schema.user_location_reviews;
CREATE TRIGGER update_user_location_reviews_updated_at
  BEFORE UPDATE ON backend_schema.user_location_reviews
  FOR EACH ROW EXECUTE FUNCTION backend_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_added_locations_updated_at ON backend_schema.user_added_locations;
CREATE TRIGGER update_user_added_locations_updated_at
  BEFORE UPDATE ON backend_schema.user_added_locations
  FOR EACH ROW EXECUTE FUNCTION backend_schema.update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_location_photos_updated_at ON backend_schema.user_location_photos;
CREATE TRIGGER update_user_location_photos_updated_at
  BEFORE UPDATE ON backend_schema.user_location_photos
  FOR EACH ROW EXECUTE FUNCTION backend_schema.update_updated_at_column();

-- Trigger function for user_interactions
CREATE OR REPLACE FUNCTION backend_schema.update_user_interactions_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_user_interactions_timestamp ON backend_schema.user_interactions;
CREATE TRIGGER update_user_interactions_timestamp
  BEFORE UPDATE ON backend_schema.user_interactions
  FOR EACH ROW EXECUTE FUNCTION backend_schema.update_user_interactions_timestamp();

-- Backend indexes
-- NextAuth users indexes
CREATE INDEX IF NOT EXISTS idx_nextauth_users_username ON backend_schema.nextauth_users(username);
CREATE INDEX IF NOT EXISTS idx_nextauth_users_email ON backend_schema.nextauth_users(email);

CREATE INDEX IF NOT EXISTS idx_user_credits_user_id ON backend_schema.user_credits(user_id);

-- Indexes for credit transactions
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id ON backend_schema.credit_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_type ON backend_schema.credit_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_created_at ON backend_schema.credit_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_type_date ON backend_schema.credit_transactions(user_id, transaction_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_related_entity ON backend_schema.credit_transactions(related_entity_type, related_entity_id);

-- Indexes for daily limits
CREATE INDEX IF NOT EXISTS idx_user_daily_limits_user_date ON backend_schema.user_daily_limits(user_id, limit_date);
CREATE INDEX IF NOT EXISTS idx_user_daily_limits_date ON backend_schema.user_daily_limits(limit_date);
CREATE INDEX IF NOT EXISTS idx_user_location_settings_user_id ON backend_schema.user_location_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_favorites_user_id ON backend_schema.user_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_user_favorites_poi_id ON backend_schema.user_favorites(poi_id);
-- Note: user_poi_submissions indexes removed - table functionality moved to spatial_schema.user_pois_temp
CREATE INDEX IF NOT EXISTS idx_user_location_visits_user_id ON backend_schema.user_location_visits(user_id);
CREATE INDEX IF NOT EXISTS idx_user_location_visits_poi_id ON backend_schema.user_location_visits(poi_id);
CREATE INDEX IF NOT EXISTS idx_user_location_reviews_user_id ON backend_schema.user_location_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_user_location_reviews_poi_id ON backend_schema.user_location_reviews(poi_id);
CREATE INDEX IF NOT EXISTS idx_user_added_locations_user_id ON backend_schema.user_added_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_added_locations_geom ON backend_schema.user_added_locations USING GIST (geom);
CREATE INDEX IF NOT EXISTS idx_user_location_reviews_rating ON backend_schema.user_location_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_user_location_reviews_created_at ON backend_schema.user_location_reviews(created_at);
CREATE INDEX IF NOT EXISTS idx_user_location_reviews_title ON backend_schema.user_location_reviews USING gin(to_tsvector('english', review_title));
CREATE INDEX IF NOT EXISTS idx_user_location_reviews_tags ON backend_schema.user_location_reviews USING gin(tags);
CREATE INDEX IF NOT EXISTS idx_user_location_visits_visit_date ON backend_schema.user_location_visits(visit_date);
CREATE INDEX IF NOT EXISTS idx_user_added_locations_verification_status ON backend_schema.user_added_locations(verification_status);

-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_user_location_reviews_user_poi ON backend_schema.user_location_reviews(user_id, poi_id);
CREATE INDEX IF NOT EXISTS idx_user_location_visits_user_poi ON backend_schema.user_location_visits(user_id, poi_id);
CREATE INDEX IF NOT EXISTS idx_user_favorites_user_poi ON backend_schema.user_favorites(user_id, poi_id);
CREATE INDEX IF NOT EXISTS idx_user_location_photos_user_id ON backend_schema.user_location_photos(user_id);
CREATE INDEX IF NOT EXISTS idx_user_location_photos_poi_id ON backend_schema.user_location_photos(poi_id);

-- Indexes for user_interactions table
CREATE INDEX IF NOT EXISTS idx_user_interactions_user_id ON backend_schema.user_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_interactions_poi_id ON backend_schema.user_interactions(poi_id) WHERE poi_type = 'official';
CREATE INDEX IF NOT EXISTS idx_user_interactions_user_poi_temp_id ON backend_schema.user_interactions(user_poi_temp_id) WHERE poi_type = 'user_temp';
CREATE INDEX IF NOT EXISTS idx_user_interactions_user_poi_approved_id ON backend_schema.user_interactions(user_poi_approved_id) WHERE poi_type = 'user_approved';
CREATE INDEX IF NOT EXISTS idx_user_interactions_type ON backend_schema.user_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_user_interactions_poi_type_interaction ON backend_schema.user_interactions(poi_type, interaction_type, created_at DESC);
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_user_poi_like ON backend_schema.user_interactions(user_id, poi_id, user_poi_temp_id, user_poi_approved_id, poi_type) WHERE interaction_type = 'like';
-- Only add this if is_public exists:
DO $$ BEGIN IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'backend_schema' AND table_name = 'user_added_locations' AND column_name = 'is_public') THEN EXECUTE 'CREATE INDEX IF NOT EXISTS idx_user_added_locations_is_public ON backend_schema.user_added_locations(is_public)'; END IF; END $$;

-- Backend functions
CREATE OR REPLACE FUNCTION backend_schema.user_has_permission(
    user_id_param UUID,
    permission_name_param TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    user_permissions JSONB;
BEGIN
    SELECT role, permissions 
    INTO user_role, user_permissions
    FROM backend_schema.nextauth_users 
    WHERE id = user_id_param;

    -- Superuser with full_access: all powers
    IF user_role = 'superuser' AND user_permissions ? 'full_access' THEN
        RETURN TRUE;
    END IF;

    -- Superuser with only user_management: limited user management
    IF user_role = 'superuser' AND user_permissions ? 'user_management' THEN
        IF permission_name_param = 'user_management' THEN
            RETURN TRUE;
        END IF;
    END IF;

    -- Agent: only full_access is effective, user_management is ignored
    IF user_role = 'agent' AND user_permissions ? 'full_access' THEN
        IF permission_name_param = 'full_access' THEN
            RETURN TRUE;
        END IF;
    END IF;

    -- All other cases: no permission
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION backend_schema.promote_user_to_agent(
    user_id_param UUID,
    promoted_by_param UUID,
    permissions_param JSONB DEFAULT '["full_access"]'
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE backend_schema.nextauth_users 
    SET 
        role = 'agent',
        permissions = permissions_param
    WHERE id = user_id_param;

    INSERT INTO backend_schema.user_role_assignments (
        user_id, role, assigned_by, notes
    ) VALUES (
        user_id_param, 'agent', promoted_by_param, 'Promoted to agent'
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION backend_schema.promote_user_to_superuser(
    user_id_param UUID,
    promoted_by_param UUID,
    permissions_param JSONB DEFAULT '["full_access"]'
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE backend_schema.nextauth_users
    SET
        role = 'superuser',
        permissions = permissions_param
    WHERE id = user_id_param;

    INSERT INTO backend_schema.user_role_assignments (
        user_id, role, assigned_by, notes
    ) VALUES (
        user_id_param, 'superuser', promoted_by_param, 'Promoted to superuser'
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION backend_schema.log_agent_activity(
    agent_id_param UUID,
    action_param TEXT,
    target_type_param TEXT DEFAULT NULL,
    target_id_param TEXT DEFAULT NULL,
    details_param JSONB DEFAULT '{}',
    ip_address_param INET DEFAULT NULL,
    user_agent_param TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    INSERT INTO backend_schema.agent_activity_log (
        agent_id, action, target_type, target_id, details, ip_address, user_agent
    ) VALUES (
        agent_id_param, action_param, target_type_param, target_id_param, 
        details_param, ip_address_param, user_agent_param
    );
END;
$$ LANGUAGE plpgsql;

-- Credit management functions
CREATE OR REPLACE FUNCTION backend_schema.user_has_credits(
    user_id_param UUID,
    required_credits INTEGER DEFAULT 1
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    available_credits INTEGER;
BEGIN
    -- Get user role
    SELECT role INTO user_role
    FROM backend_schema.nextauth_users
    WHERE id = user_id_param;

    -- Agents and superusers bypass credit requirements
    IF user_role IN ('agent', 'superuser') THEN
        RETURN TRUE;
    END IF;

    -- Calculate available credits for regular users
    SELECT COALESCE(credits_earned + credits_purchased - credits_used, 0)
    INTO available_credits
    FROM backend_schema.user_credits
    WHERE user_id = user_id_param;

    RETURN available_credits >= required_credits;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION backend_schema.deduct_credits(
    user_id_param UUID,
    amount INTEGER,
    reason_param TEXT,
    description_param TEXT DEFAULT NULL,
    related_entity_type_param VARCHAR(50) DEFAULT NULL,
    related_entity_id_param TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    available_credits INTEGER;
BEGIN
    -- Get user role
    SELECT role INTO user_role
    FROM backend_schema.nextauth_users
    WHERE id = user_id_param;

    -- Agents and superusers bypass credit deduction
    IF user_role IN ('agent', 'superuser') THEN
        RETURN TRUE;
    END IF;

    -- Check if user has enough credits
    IF NOT backend_schema.user_has_credits(user_id_param, amount) THEN
        RETURN FALSE;
    END IF;

    -- Update credits_used
    UPDATE backend_schema.user_credits
    SET credits_used = credits_used + amount,
        updated_at = NOW()
    WHERE user_id = user_id_param;

    -- Record transaction
    INSERT INTO backend_schema.credit_transactions (
        user_id, transaction_type, amount, reason, description,
        related_entity_type, related_entity_id
    ) VALUES (
        user_id_param, 'use', amount, reason_param, description_param,
        related_entity_type_param, related_entity_id_param
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION backend_schema.award_credits(
    user_id_param UUID,
    amount INTEGER,
    reason_param TEXT,
    description_param TEXT DEFAULT NULL,
    related_entity_type_param VARCHAR(50) DEFAULT NULL,
    related_entity_id_param TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    -- Update credits_earned
    UPDATE backend_schema.user_credits
    SET credits_earned = credits_earned + amount,
        updated_at = NOW()
    WHERE user_id = user_id_param;

    -- If no record exists, create one
    IF NOT FOUND THEN
        INSERT INTO backend_schema.user_credits (
            user_id, credits_earned, credits_purchased, credits_used
        ) VALUES (
            user_id_param, amount, 0, 0
        );
    END IF;

    -- Record transaction
    INSERT INTO backend_schema.credit_transactions (
        user_id, transaction_type, amount, reason, description,
        related_entity_type, related_entity_id
    ) VALUES (
        user_id_param, 'earn', amount, reason_param, description_param,
        related_entity_type_param, related_entity_id_param
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Daily limit management functions
CREATE OR REPLACE FUNCTION backend_schema.check_daily_limit(
    user_id_param UUID,
    limit_type VARCHAR(20), -- 'likes', 'comments', 'poi_submissions'
    max_limit INTEGER DEFAULT 10
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    current_count INTEGER;
    today_date DATE := CURRENT_DATE;
BEGIN
    -- Get user role
    SELECT role INTO user_role
    FROM backend_schema.nextauth_users
    WHERE id = user_id_param;

    -- Agents and superusers bypass daily limits
    IF user_role IN ('agent', 'superuser') THEN
        RETURN TRUE;
    END IF;

    -- Get current count for today
    SELECT CASE
        WHEN limit_type = 'likes' THEN COALESCE(likes_count, 0)
        WHEN limit_type = 'comments' THEN COALESCE(comments_count, 0)
        WHEN limit_type = 'poi_submissions' THEN COALESCE(poi_submissions_count, 0)
        ELSE 0
    END INTO current_count
    FROM backend_schema.user_daily_limits
    WHERE user_id = user_id_param AND limit_date = today_date;

    -- If no record exists, user hasn't hit any limits yet
    IF current_count IS NULL THEN
        RETURN TRUE;
    END IF;

    RETURN current_count < max_limit;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION backend_schema.increment_daily_limit(
    user_id_param UUID,
    limit_type VARCHAR(20) -- 'likes', 'comments', 'poi_submissions'
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    today_date DATE := CURRENT_DATE;
BEGIN
    -- Get user role
    SELECT role INTO user_role
    FROM backend_schema.nextauth_users
    WHERE id = user_id_param;

    -- Agents and superusers bypass daily limits
    IF user_role IN ('agent', 'superuser') THEN
        RETURN TRUE;
    END IF;

    -- Insert or update daily limits
    INSERT INTO backend_schema.user_daily_limits (
        user_id, limit_date, likes_count, comments_count, poi_submissions_count
    ) VALUES (
        user_id_param, today_date,
        CASE WHEN limit_type = 'likes' THEN 1 ELSE 0 END,
        CASE WHEN limit_type = 'comments' THEN 1 ELSE 0 END,
        CASE WHEN limit_type = 'poi_submissions' THEN 1 ELSE 0 END
    )
    ON CONFLICT (user_id, limit_date) DO UPDATE SET
        likes_count = CASE
            WHEN limit_type = 'likes' THEN backend_schema.user_daily_limits.likes_count + 1
            ELSE backend_schema.user_daily_limits.likes_count
        END,
        comments_count = CASE
            WHEN limit_type = 'comments' THEN backend_schema.user_daily_limits.comments_count + 1
            ELSE backend_schema.user_daily_limits.comments_count
        END,
        poi_submissions_count = CASE
            WHEN limit_type = 'poi_submissions' THEN backend_schema.user_daily_limits.poi_submissions_count + 1
            ELSE backend_schema.user_daily_limits.poi_submissions_count
        END,
        updated_at = NOW();

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Access control functions
CREATE OR REPLACE FUNCTION backend_schema.is_superuser(
    user_id_param UUID
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM backend_schema.nextauth_users
    WHERE id = user_id_param;

    RETURN user_role = 'superuser';
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION backend_schema.can_access_admin_panel(
    user_id_param UUID
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    user_permissions JSONB;
BEGIN
    SELECT role, permissions
    INTO user_role, user_permissions
    FROM backend_schema.nextauth_users
    WHERE id = user_id_param;

    -- Only superusers with appropriate permissions can access admin panel
    IF user_role = 'superuser' THEN
        -- Allow if user has full_access or user_management
        RETURN user_permissions ? 'full_access' OR user_permissions ? 'user_management';
    END IF;

    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION backend_schema.can_access_agent_dashboard(
    user_id_param UUID
) RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    user_permissions JSONB;
BEGIN
    SELECT role, permissions
    INTO user_role, user_permissions
    FROM backend_schema.nextauth_users
    WHERE id = user_id_param;

    -- Agents with full_access can access agent dashboard
    IF user_role = 'agent' AND user_permissions ? 'full_access' THEN
        RETURN TRUE;
    END IF;

    -- Superusers with full_access can access agent dashboard
    IF user_role = 'superuser' AND user_permissions ? 'full_access' THEN
        RETURN TRUE;
    END IF;

    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Insert default permissions
INSERT INTO backend_schema.agent_permissions (permission_name, permission_description, permission_category) VALUES
('full_access', 'Full access to all data and user management (role-aware)', 'core'),
('user_management', 'Limited user management (promote/demote agents only)', 'core')
ON CONFLICT (permission_name) DO NOTHING;
